/**
 * 测试微信适配器配置保存功能
 * 特别是智能消息缓存配置的保存和加载
 */

const axios = require('axios');

const API_BASE = 'http://localhost:6005/admin_api/wechat';

async function testConfigSaveAndLoad() {
    console.log('🧪 开始测试微信适配器配置保存功能...\n');

    try {
        // 1. 获取当前配置
        console.log('📖 1. 获取当前配置...');
        const currentConfigResponse = await axios.get(`${API_BASE}/config`);
        console.log('✅ 当前配置获取成功');
        
        const currentConfig = currentConfigResponse.data.data;
        console.log('📋 当前消息缓存配置:', JSON.stringify(currentConfig.AI_AUTO_REPLY?.message_cache || '未配置', null, 2));

        // 2. 准备测试配置（包含消息缓存配置）
        console.log('\n🔧 2. 准备测试配置...');
        const testConfig = {
            ...currentConfig,
            AI_AUTO_REPLY: {
                ...currentConfig.AI_AUTO_REPLY,
                message_cache: {
                    enabled: true,
                    wait_time: 8,
                    max_wait_cycles: 5,
                    debug: true
                }
            }
        };
        console.log('✅ 测试配置准备完成');
        console.log('📋 新的消息缓存配置:', JSON.stringify(testConfig.AI_AUTO_REPLY.message_cache, null, 2));

        // 3. 保存配置
        console.log('\n💾 3. 保存配置...');
        const saveResponse = await axios.post(`${API_BASE}/config`, testConfig);
        console.log('✅ 配置保存响应:', saveResponse.data);

        // 4. 重新获取配置验证
        console.log('\n🔍 4. 重新获取配置验证...');
        const verifyConfigResponse = await axios.get(`${API_BASE}/config`);
        const verifiedConfig = verifyConfigResponse.data.data;
        
        console.log('📋 验证后的消息缓存配置:', JSON.stringify(verifiedConfig.AI_AUTO_REPLY?.message_cache || '未配置', null, 2));

        // 5. 验证配置是否正确保存
        console.log('\n✅ 5. 验证结果...');
        const savedCache = verifiedConfig.AI_AUTO_REPLY?.message_cache;
        
        if (!savedCache) {
            console.log('❌ 错误：消息缓存配置未保存');
            return false;
        }

        const checks = [
            { name: 'enabled', expected: true, actual: savedCache.enabled },
            { name: 'wait_time', expected: 8, actual: savedCache.wait_time },
            { name: 'max_wait_cycles', expected: 5, actual: savedCache.max_wait_cycles },
            { name: 'debug', expected: true, actual: savedCache.debug }
        ];

        let allPassed = true;
        checks.forEach(check => {
            if (check.actual === check.expected) {
                console.log(`✅ ${check.name}: ${check.actual} (正确)`);
            } else {
                console.log(`❌ ${check.name}: 期望 ${check.expected}, 实际 ${check.actual} (错误)`);
                allPassed = false;
            }
        });

        if (allPassed) {
            console.log('\n🎉 测试通过！消息缓存配置保存功能正常工作');
        } else {
            console.log('\n💥 测试失败！消息缓存配置保存存在问题');
        }

        return allPassed;

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('📋 错误响应:', error.response.data);
        }
        return false;
    }
}

// 运行测试
testConfigSaveAndLoad().then(success => {
    process.exit(success ? 0 : 1);
});
